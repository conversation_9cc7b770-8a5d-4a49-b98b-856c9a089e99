package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqKnowledgePO;
import com.faw.work.ais.aic.model.domain.FaqRobotKnowledgeJoinsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ机器人知识关联Mapper接口
 * 用于访问和操作FAQ机器人与知识之间的关联关系数据表。
 * <AUTHOR>
 */
@Mapper
public interface FaqRobotKnowledgeJoinsMapper extends BaseMapper<FaqRobotKnowledgeJoinsPO> {

    /**
     * 批量保存机器人知识关联。
     * 将指定的知识ID列表与指定的机器人ID进行关联，实现知识与机器人的绑定。
     *
     * @param robotId 机器人ID。
     * @param knowledgeIds 知识ID列表。
     * @param source 关联来源，例如：手动配置、自动学习等。
     * @return 保存结果，表示成功关联的记录数。
     */
    int bindKnowledge(@Param("robotId") String robotId, @Param("knowledgeIds") List<String> knowledgeIds, @Param("source") String source);

    /**
     * 根据机器人ID删除所有关联的知识。
     * 用于解除机器人与所有知识的绑定关系。
     *
     * @param robotId 机器人ID。
     * @return 删除结果，表示成功删除的记录数。
     */
    int deleteByRobotId(@Param("robotId") String robotId);

    /**
     * 根据机器人ID查询关联的知识列表。
     * 用于获取指定机器人所关联的所有知识。
     *
     * @param robotId 机器人ID。
     * @return 知识列表，包含与指定机器人关联的所有知识信息。
     */
    List<FaqKnowledgePO> selectKnowledgesByRobotId(@Param("robotId") String robotId);

    /**
     * 根据机器人ID查询所有关联的ID。
     * 用于获取指定机器人所关联的所有知识ID。
     *
     * @param robotId 机器人ID。
     * @return ID列表，包含与指定机器人关联的所有知识ID。
     */
    List<String> selectIdsByRobotId(@Param("robotId") String robotId);

    /**
     * 根据知识类目ID删除关联记录。
     * 用于解除指定知识类目下的所有知识与机器人的绑定关系。
     *
     * @param categoryId 知识类目ID。
     * @return 删除结果，表示成功删除的记录数。
     */
    int deleteByKnowledgeCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据知识ID列表查询关联记录。
     *
     * @param knowledgeIds 知识ID列表
     * @return 关联记录列表
     */
    List<FaqRobotKnowledgeJoinsPO> selectByKnowledgeIds(@Param("knowledgeIds") List<String> knowledgeIds);

    /**
     * 根据知识ID列表删除关联记录。
     *
     * @param allKnowledgeIds 知识ID列表
     */
    void deleteByKnowledgeIds(@Param("allKnowledgeIds") List<String> allKnowledgeIds);

    /**
     * 根据类目ID查询关联的机器人ID列表。
     * 用于查询指定类目下的知识与哪些机器人相关联。
     *
     * @param categoryId 知识类目ID
     * @return 机器人ID列表
     */
    List<String> selectRobotIdsByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据机器人ID查询关联的类目ID列表。
     *
     * @param robotId 机器人ID
     * @return 类目ID列表
     */
    List<String> selectCategoryIdsByRobotId(@Param("robotId") String robotId);

    /**
     * 根据机器人ID查询生产环境关联的知识ID列表。
     *
     * @param robotId 机器人ID
     * @return 知识ID列表
     */
    List<String> selectProdIdsByRobotId(@Param("robotId") String robotId);

    /**
     * 根据机器人ID删除生产环境关联。
     *
     * @param robotId 机器人ID
     * @return 删除结果
     */
    int deleteProdByRobotId(@Param("robotId") String robotId);

    /**
     * 根据机器人ID查询生产环境关联的类目ID列表。
     *
     * @param robotId 机器人ID
     * @return 类目ID列表
     */
    List<String> selectProdCategoryIdsByRobotId(@Param("robotId") String robotId);

    /**
     * 根据关联ID列表查询生产环境关联记录，并返回知识ID和问题映射。
     *
     * @param intersectionJoinIds 关联ID列表
     * @return 关联记录列表
     */
    List<FaqRobotKnowledgeJoinsPO> selectProdJoinIdWithQuestionMapByJoinIds(@Param("intersectionJoinIds") List<String> intersectionJoinIds);

    /**
     * 根据机器人ID和知识ID查询关联记录。
     *
     * @param robotId 机器人ID
     * @param knowledgeId 知识ID
     * @return 关联记录列表
     */
    List<FaqRobotKnowledgeJoinsPO> selectByRobotId(@Param("robotId") String robotId, @Param("knowledgeId") String knowledgeId);

    /**
     * 查询与指定知识ID关联的不同机器人ID列表。
     *
     * @param knowledgeId 知识ID
     * @return 机器人ID列表
     */
    List<String> selectDistinctRobotId(@Param("knowledgeId") String knowledgeId);

    /**
     * 通过知识id选择不同机器人id
     *
     * @param similarIds 相似id
     * @return {@link List }<{@link String }>
     */
    List<String> selectDistinctRobotIdsByKnowledgeIds(@Param("similarIds") List<String> similarIds);

    /**
     * 按机器人id和知识id选择
     *
     * @param robotId         机器人id
     * @param allKnowledgeIds 所有知识ID
     * @return {@link List }<{@link FaqRobotKnowledgeJoinsPO }>
     */
    List<FaqRobotKnowledgeJoinsPO> selectByRobotIdAndKnowledgeIds(@Param("robotId") String robotId, @Param("allKnowledgeIds") List<String> allKnowledgeIds);

    /**
     * 根据知识ID查询关联记录
     *
     * @param kid 知识ID
     * @return {@link List }<{@link FaqRobotKnowledgeJoinsPO }>
     */
    List<FaqRobotKnowledgeJoinsPO> selectByKnowledgeId(@Param("kid") String kid);

    /**
     * 根据机器人ID查询关联的知识ID列表。
     *
     * @param robotId 机器人ID
     * @param env 环境标识
     * @return 知识ID列表
     */
    List<String> findKnowledgeIdsByRobotId(@Param("robotId") String robotId, @Param("env") String env);

}
