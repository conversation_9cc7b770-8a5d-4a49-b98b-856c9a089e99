package com.faw.work.ais.config.chat;

import com.faw.work.ais.common.PromptConstants;
import com.faw.work.ais.mapper.chat.AppChatHistoryDetailMapper;
import com.faw.work.ais.mapper.chat.AppChatHistoryMapper;
import com.alibaba.cloud.ai.dashscope.api.DashScopeApi;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatModel;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * AI聊天初始化
 *
 * <AUTHOR>
 * @since 2025-04-03 15:53
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChatInit {

    private final ChatModel chatModel;


    @Bean
    public ChatClient chatClient() {
        return ChatClient.builder(chatModel).defaultSystem(PromptConstants.CHAT_SYSTEM_PROMPT).build();
    }

    @Bean
    public ChatClient universalClient() {
        return ChatClient.builder(chatModel).defaultSystem(PromptConstants.UNIVERSAL_SYSTEM_PROMPT).build();
    }

    @Bean
    public ChatMemory chatMemory(RedisTemplate<String, Object> redisTemplate, AppChatHistoryMapper appChatHistoryMapper,
                                 AppChatHistoryDetailMapper appChatHistoryDetailMapper) {
        return new ChatRedisMemory(redisTemplate, appChatHistoryMapper, appChatHistoryDetailMapper);
    }


    @Bean
    public ChatClient reQueryClient(DashScopeApi dashScopeApi) {

        DashScopeChatOptions option = DashScopeChatOptions.builder()
                .withModel("qwen-turbo")
                .withTemperature(0.01)
                .withTopP(0.01)
                .withSeed(1)
                .build();

        ChatModel dashScopeChatModel = new DashScopeChatModel(dashScopeApi,option);

        return ChatClient.builder(dashScopeChatModel)
                .defaultSystem("" +
                        "作为文本内容格式化专家，你的任务是针对用户上传的文本进行格式调整，以提高其易读性。请遵循以下指导原则： " +
                            "- 不改变原文本的语义。" +
                            "- 一定一定一定不能修改文本的内容，但是你可以适当增加序号确保可读性。不要使用md格式，例如'##' " +
                            "- 可以适当增加序号来增强结构清晰度。 " +
                            "- 去除无关信息，如页码、日期和时间戳等（例如：“E009 OM.book Page 298 Monday, February 24, 2025 2:27 PM”）。 " +
                            "- 输出的文本不要换行，压缩到一行 " +
                        "请根据上述要求对提供的文本进行格式化处理。")
                .defaultAdvisors(new SimpleLoggerAdvisor())  // 日志记录
                .build();
    }

}
