package com.faw.work.ais.aic.feign;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSONObject;
import com.faw.work.ais.aic.common.constant.AicCommonConstants;
import com.faw.work.ais.aic.config.AicConfig;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.common.util.UcgTokenutils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.time.Duration;

/**
 * <AUTHOR>
 * 调用工作流和角色工作台feign接口拦截器
 */
@Slf4j
public class AicAuthFeignInterceptor implements RequestInterceptor {

    @Resource
    private AicConfig aicConfig;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 路径参数添加token
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        String token = getToken();
        requestTemplate.query("access_token", token);
    }

    /**
     * 获取token，优先从缓存获取，缓存不存在或异常时重新获取
     */
    private String getToken() {
        // 先尝试从Redis获取token
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String TOKEN_KEY = "ucg:aic:gatewayToken";
        RBucket<String> tokenBucket = redissonClient.getBucket(TOKEN_KEY);
        stopWatch.stop();
        log.info("从Redis获取token耗时：" + stopWatch.getTotalTimeMillis() + "ms");
        if (tokenBucket.isExists()) {
            return tokenBucket.get();
        } else {
            return fetchAndCacheToken(tokenBucket);

        }
    }

    /**
     * 获取新token并缓存
     */
    private String fetchAndCacheToken(RBucket<String> tokenBucket) {
        try {
            String environment = aicConfig.getEnvironment();
            log.info("当前运行环境：" + environment);
            JSONObject jsonObject = UcgTokenutils.getUcgAccessToken(environment, aicConfig.getAppKey(), aicConfig.getAppSecret());
            log.info("调用工作台获取的token：" + jsonObject);

            Integer successCode = 200;
            if (successCode.equals(jsonObject.getInteger(AicCommonConstants.CODE))) {
                String token = jsonObject.getJSONObject("data").getString("access_token");

                // 缓存token，TTL设置为30分钟
                try {
                    tokenBucket.set(token, Duration.ofMinutes(30));
                } catch (Exception e) {
                    log.warn("缓存token失败，但继续使用获取的token", e);
                }

                return token;
            } else {
                log.error("获取工作台token失败：" + jsonObject);
                throw new BizException("获取工作台token失败：" + jsonObject);
            }
        } catch (Exception e) {
            log.error("获取token失败", e);
            throw new BizException("获取token失败：" + e.getMessage());
        }
    }
}