package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.model.domain.MessageQueue;

/**
 * 消息队列服务
 *
 * <AUTHOR>
 */
public interface MessageQueueService extends IService<MessageQueue> {



    /**
     * 更新状态
     *
     * @param messageId 消息id
     * @param code      状态
     * @param remark    备注
     */
    void updateStatus(String messageId, MessageStatus code,String remark);
}