package com.faw.work.ais.aic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import com.faw.work.ais.aic.service.MessageQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 消息队列服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageQueueServiceImpl extends ServiceImpl<MessageQueueMapper, MessageQueue> implements MessageQueueService {

    @Autowired
    private MessageQueueMapper messageQueueMapper;


    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void updateStatus(String messageId, MessageStatus code,String remark) {
        MessageQueue messageQueue = messageQueueMapper.selectByMessageId(messageId);
        if (messageQueue == null) {
            log.error("消息队列不存在，消息ID：{}", messageId);
            return;
        }
        messageQueue.setStatus(code.getCode());
        messageQueue.setRemark(remark);
        messageQueue.setUpdateTime(LocalDateTime.now());
        messageQueueMapper.updateById(messageQueue);


    }
} 