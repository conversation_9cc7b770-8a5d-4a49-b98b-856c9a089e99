package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.request.RetryRequest;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;
import com.faw.work.ais.aic.service.LlmRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/llm-record")
@Slf4j
@Tag(name = "LlmRecordController", description = "处理外部调用大模型请求")
public class LlmRecordController {
    @Autowired
    private LlmRecordService llmRecordService;

    /**
     * 处理对话，包括情绪分析和标签提取
     *
     * @param request 包含文本和语音COS URL的请求
     * @return 处理结果
     */
    @Operation(summary = "情绪价值模型接收消息", description = "处理对话，包括情绪分析和标签提取")
    @PostMapping("/process-conversation")
    public AiResult<String> processConversation(@Valid @RequestBody ProcessRequest request) {
        log.info("开始处理对话，请求ID: {}, 用户输入: {},  语音URL: {}", request.getRequestId(), request.getUserInput(), request.getAudioUrl());
        llmRecordService.processConversation(request);
        return AiResult.successMsg("成功接收消息,请关注回调消息");
    }



    /**
     * 重试失败的记录
     *
     * @param request 包含请求ID的请求
     * @return 处理结果
     */
    @Operation(summary = "重试失败记录", description = "根据请求ID重试所有失败的记录")
    @PostMapping("/retry-failed")
    public AiResult<String> retryFailedRecords(@Valid @RequestBody RetryRequest request) {
        log.info("开始重试失败记录，请求ID: {}", request.getRequestId());
        for (String requestId : request.getRequestId()) {
            llmRecordService.retryFailedRecords(requestId);

        }
        return AiResult.successMsg("重试成功");
    }

    /**
     * 清理已完成的消息队列数据
     *
     * @return 清理结果
     */
    @Operation(summary = "清理已完成的消息队列", description = "清理状态为3（已完成）的消息队列数据")
    @PostMapping("/clean-completed-messages")
    public AiResult<MessageQueueCleanResponse> cleanCompletedMessages() {
        log.info("开始清理已完成的消息队列数据");
        MessageQueueCleanResponse response = llmRecordService.cleanCompletedMessages();
        return AiResult.success(response);
    }
}
