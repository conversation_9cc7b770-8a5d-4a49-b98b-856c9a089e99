package com.faw.work.ais.aic.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 标签分析返回结果DTO
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TagAnalysisDTO {

    /**
     * 分片ID
     */
    private Long llmRecordId;

    /**
     * 整个对话的开始时间戳
     */
    private String start;

    /**
     * 整个对话的结束时间戳
     */
    private String end;

    /**
     * 问题总结
     */
    private List<TopicSummary> customerQuestionSummaries;

    /**
     * 客户情绪分析（语义维度）
     */
    private EmotionAnalysis semanticEmotion;

    /**
     * 客户情绪分析（语气维度）
     */
    private EmotionAnalysis toneEmotion;

    /**
     * 客户情绪分析（行为维度）
     */
    private EmotionAnalysis behaviorEmotion;

    /**
     * 需求标签
     */
    private List<DemandTag> demandTags;

    /**
     * AI生成的需求标签
     */
    private List<DemandTag> aiDemandTags;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TopicSummary {
        @JsonProperty("question")
        private String question;

        @JsonProperty("answer")
        private String answer;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DemandTag {
        @JsonProperty("level1")
        private String level1;

        @JsonProperty("level2")
        private String level2;

        @JsonProperty("level3")
        private String level3;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EmotionAnalysis {
        /**
         * 情绪分类
         */
        @JsonProperty("emotion")
        private String emotion;

        /**
         * 情绪得分
         */
        @JsonProperty("score")
        private String score;

        /**
         * 情绪原因
         */
        @JsonProperty("reason")
        private String reason;
    }
}