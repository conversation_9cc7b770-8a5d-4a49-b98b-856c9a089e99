package com.faw.work.ais.aic.model.response;

import com.faw.work.ais.aic.model.dto.TagAnalysisDTO;
import com.faw.work.ais.aic.model.dto.TopicSummaryDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DmsEmotionResponse {

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 整个接待情绪总结
     */
    private String emotionSummary;

    /**
     * 标签数据-产品需求模型
     */
    private List<TagAnalysisDTO> tagData;

    /**
     * 话题总结数据-话题总结模型
     */
    private TopicSummaryDTO topicData;
}
