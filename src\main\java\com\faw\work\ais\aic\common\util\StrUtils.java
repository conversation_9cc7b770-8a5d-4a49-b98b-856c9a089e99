package com.faw.work.ais.aic.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public class StrUtils {

    public static final String JSON_TAG = "```json";

    /**
     * 清理文本，移除多余空格和处理多行换行
     * @param rawText 原始文本
     * @return 处理后的文本
     */
    public static String cleanText(String rawText) {
        if (rawText == null) {
            return "";
        }

        // 首先将多行换行替换为单个换行符
        String singleLineBreak = rawText.replaceAll("\\n+", "\n");

        // 然后替换所有空白字符（包括空格、制表符等，但不包括刚处理的换行符）为单个空格
        String normalized = singleLineBreak.replaceAll("\\s&&[^\n" +
                "]+", " ");

        // 移除首尾空格
        return normalized.trim();
    }





    /**
     * 修改后的核心分片方法。
     * 分片逻辑：一个分片包含至少N个客户句子，并会一直延伸，包含后面所有连续的专员回答，
     * 直到遇到下一个客户句子或对话结束为止。
     *
     * @param fullConversation     完整的对话文本
     * @param customerSpeaker      客户的说话人标识
     * @param minCustomerSentences 一个分片中最少应包含的客户发言次数
     * @param overlapLines         分片间的重叠行数
     * @return 一个有序的Map，Key是分片内容，Value是拼接好的时间戳字符串（例如 "HH:MM:SS-HH:MM:SS,...")
     */
    public static Map<String, String> sliceConversation(String fullConversation,
                                                        String customerSpeaker,
                                                        int minCustomerSentences,
                                                        int overlapLines) {
        if (StrUtil.isBlank(fullConversation)) {
            return new LinkedHashMap<>();
        }

        final Pattern timePattern = Pattern.compile("^(\\d{2}:\\d{2}:\\d{2})\\s");
        final Map<String, String> slicesWithTimestamps = new LinkedHashMap<>();

        // 1. 预处理：分割行并提取每行的开始时间
        String[] lines = fullConversation.split("\\r?\\n");
        List<String> allLines = new ArrayList<>();
        List<String> lineStartTimes = new ArrayList<>();
        for (String line : lines) {
            if (StrUtil.isNotBlank(line)) {
                allLines.add(line);
                Matcher matcher = timePattern.matcher(line);
                if (matcher.find()) {
                    lineStartTimes.add(matcher.group(1));
                } else {
                    lineStartTimes.add(null);
                }
            }
        }

        if (allLines.isEmpty()) {
            return new LinkedHashMap<>();
        }

        // 2. 核心分片循环
        int startIndex = 0;
        while (startIndex < allLines.size()) {
            // --- 阶段一：找到满足最小客户发言次数的块 ---
            int customerCount = 0;
            int minEndPoint = -1;
            for (int i = startIndex; i < allLines.size(); i++) {
                if (isCustomerLine(allLines.get(i), customerSpeaker)) {
                    customerCount++;
                }
                if (customerCount >= minCustomerSentences) {
                    minEndPoint = i;
                    break;
                }
            }

            // 如果找不到包含足够客户发言的块，则将剩余所有内容作为一个分片
            if (minEndPoint == -1) {
                minEndPoint = allLines.size() - 1;
            }

            // --- 阶段二：从最小结束点向后延伸，直到遇到下一个客户发言 ---
            int finalSliceEndIndex = minEndPoint;
            for (int i = minEndPoint + 1; i < allLines.size(); i++) {
                if (isCustomerLine(allLines.get(i), customerSpeaker)) {
                    // 遇到下一个客户发言，停止延伸
                    break;
                }
                // 如果是专员发言，则将分片边界向后推
                finalSliceEndIndex = i;
            }

            // 3. 根据最终确定的 [startIndex, finalSliceEndIndex] 来生成分片和时间戳
            StringBuilder sliceContentBuilder = new StringBuilder();
            List<String> timestampPairs = new ArrayList<>();

            for (int k = startIndex; k <= finalSliceEndIndex; k++) {
                sliceContentBuilder.append(allLines.get(k)).append("\n");

                if (isCustomerLine(allLines.get(k), customerSpeaker)) {
                    String startTime = lineStartTimes.get(k);
                    // 结束时间为下一行的开始时间，或自身的开始时间（如果是最后一行）
                    String endTime = (k + 1 < allLines.size()) ? lineStartTimes.get(k + 1) : startTime;
                    if (endTime == null) {
                        endTime = startTime;
                    }

                    if (startTime != null) {
                        timestampPairs.add(startTime + "-" + endTime);
                    }
                }
            }

            String finalSliceContent = sliceContentBuilder.toString().trim();
            String finalTimestamps = String.join(",", timestampPairs);

            if (StrUtil.isNotBlank(finalSliceContent)) {
                slicesWithTimestamps.put(finalSliceContent, finalTimestamps);
            }

            // 4. 计算下一个分片的起始位置
            // 如果已经处理到最后，则退出
            if (finalSliceEndIndex >= allLines.size() - 1) {
                break;
            }
            startIndex = Math.max(0, finalSliceEndIndex - overlapLines + 1);
        }

        return slicesWithTimestamps;
    }


    /**
     * 辅助方法，判断某一行是否为客户发言
     */
    private static boolean isCustomerLine(String line, String customerSpeaker) {
        // 兼容全角和半角冒号
        return line.contains(customerSpeaker + ":") || line.contains(customerSpeaker + "：");
    }


    /**
     * 剔除字符串中多余的代码块标记，只保留纯JSON内容
     * @param input 可能包含多余标记的字符串
     * @return 清理后的纯JSON字符串
     */
    public static String cleanJsonString(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        // 如果是合法json字符串，直接返回，避免出现问题
        if(!input.contains(JSON_TAG)){
            return input;
        }

        // 定义匹配```json和```的正则表达式
        Pattern pattern = Pattern.compile("^\\s*```json\\s*|\\s*```\\s*$", Pattern.MULTILINE);

        // 替换掉这些标记
        String cleaned = pattern.matcher(input).replaceAll("");

        // 去除首尾空白字符
        return cleaned.trim();
    }

}