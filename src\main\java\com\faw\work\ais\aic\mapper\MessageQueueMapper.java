package com.faw.work.ais.aic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MessageQueueMapper extends BaseMapper<MessageQueue> {
    /**
     * 按消息id选择
     *
     * @param messageId 消息id
     * @return {@link MessageQueue }
     */
    @Transactional(isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    MessageQueue selectByMessageId(String messageId);

    List<MessageQueue> selectByMessageContent(String messageContent);
}