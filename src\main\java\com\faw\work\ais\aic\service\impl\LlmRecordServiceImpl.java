package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.LLMBizTypeEnum;
import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.common.mq.MessageProducer;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.DateUtils;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.feign.DgwOpenApiFeignClient;
import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.mapper.llm.LlmRecordMapper;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import com.faw.work.ais.aic.model.dto.LlmAnalysisResultDTO;
import com.faw.work.ais.aic.model.dto.TagAnalysisDTO;
import com.faw.work.ais.aic.model.dto.TopicSummaryDTO;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.response.DmsEmotionResponse;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 情绪分析表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LlmRecordServiceImpl extends ServiceImpl<LlmRecordMapper, LlmRecord> implements LlmRecordService {

    private static final String STATUS_UNPROCESSED = "00";
    private static final String STATUS_PROCESSING = "01";
    private static final String STATUS_COMPLETED = "02";
    private static final String STATUS_FAILED = "10";

    @Autowired
    private DgwOpenApiFeignClient dgwFeignClient;
    @Autowired
    private LlmRecordMapper llmRecordMapper;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private BaiLianAppConfig baiLianAppConfig;
    @Autowired
    private MessageQueueMapper messageQueueMapper;
    @Autowired
    private LlmRecordService self;
    @Autowired
    private Executor dmsEmotionExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processConversation(ProcessRequest request) {
        try {
            String requestId = request.getRequestId();
            List<MessageQueue> messageQueues = messageQueueMapper.selectByMessageContent(requestId);
            if (CollUtil.isNotEmpty(messageQueues)) {
                throw new IllegalArgumentException("请求ID已存在，请勿重复提交");
            }

            String conversationContent;
            String textUrl = request.getTextUrl();

            if (StrUtil.isNotBlank(textUrl)) {
                conversationContent = downloadAndReadCosFile(textUrl);
            } else {
                conversationContent = request.getUserInput();
            }

            String customerSpeaker = "客户";

            // --- 修改点 1: 调用新的分片方法，接收 Map ---
            Map<String, String> slices = StrUtils.sliceConversation(conversationContent, customerSpeaker, 5, 1);

            if (slices.isEmpty()) {
                throw new IllegalArgumentException("对话内容中没有客户的说话内容，或无法进行有效分片");
            }

            List<LlmRecord> emotionList = new ArrayList<>();
            String now = DateUtils.getCurrentDateTimeString();

            // --- 修改点 2: 遍历 Map 创建 LlmRecord ---
            for (Map.Entry<String, String> entry : slices.entrySet()) {
                String sliceContent = entry.getKey();
                String timestamps = entry.getValue();

                LlmRecord emotion = new LlmRecord();
                emotion.setRequestId(requestId);

                // 设置分片内容
                emotion.setUserInput(sliceContent);

                emotion.setStatus(STATUS_UNPROCESSED);
                emotion.setBizType(LLMBizTypeEnum.DMS_EMOTION.getCode());
                emotion.setCallbackUrl("/JT/SA/SA-0214/DGW/serviceInspection/aiSummaryCallback");
                emotion.setField1(textUrl);
                emotion.setField2(request.getAudioUrl());

                // --- 修改点 3: 将拼接好的时间戳字符串存入 field3 ---
                emotion.setField3(timestamps);

                emotion.setCreateAt(now);
                emotionList.add(emotion);
            }

            this.saveBatch(emotionList);
            log.info("成功将 {} 个统一分析对话分片存入数据库", emotionList.size());

            messageProducer.sendMessage(requestId, MessageTypeEnum.MQ_TYPE_DMS_EMOTION.getCode());
        } catch (Exception e) {
            log.error("处理请求失败", e);
            throw new BizException(e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processSlice(String requestId) {
        List<LlmRecord> list = llmRecordMapper.selectByRequestId(requestId, STATUS_UNPROCESSED, LLMBizTypeEnum.DMS_EMOTION.getCode());

        if (CollUtil.isEmpty(list)) {
            log.warn("未找到需要处理的分片, requestId: {}", requestId);
            return checkAndFinalizeProcessing(requestId);
        }

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (LlmRecord slice : list) {
            log.info("开始处理分片ID: {}", slice.getId());
            self.updateStatusTran(slice.getId(), STATUS_PROCESSING, null);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    performCombinedAnalysis(slice);
                    self.updateStatusTran(slice.getId(), STATUS_COMPLETED, null);
                    log.info("分片ID: {} 处理成功", slice.getId());
                } catch (Exception e) {
                    log.error("分片ID: {} 处理失败", slice.getId(), e);
                    self.updateStatusTran(slice.getId(), STATUS_FAILED, null);
                }
            }, dmsEmotionExecutor);
            futures.add(future);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("等待异步任务完成时发生异常, requestId: {}", requestId, e);
        }

        return assembleAndCallback(requestId);
    }

    private boolean checkAndFinalizeProcessing(String requestId) {
        long totalCount = llmRecordMapper.selectCount(new LambdaQueryWrapper<LlmRecord>().eq(LlmRecord::getRequestId, requestId));
        if (totalCount == 0) {
            log.warn("请求ID {} 没有任何分片记录。", requestId);
            return true;
        }
        long completedCount = llmRecordMapper.selectCount(new LambdaQueryWrapper<LlmRecord>().eq(LlmRecord::getRequestId, requestId).eq(LlmRecord::getStatus, STATUS_COMPLETED));
        if (totalCount == completedCount) {
            log.info("请求ID: {} 的所有分片均已处理完成，开始组装回调。", requestId);
            return assembleAndCallback(requestId);
        }
        return true;
    }

    private boolean assembleAndCallback(String requestId) {
        log.info("开始组装数据并执行回调，请求ID: {}", requestId);

        // 3. 执行话题总结，这个是针对所有分片的，如果失败则影响全局
        TopicSummaryDTO topicSummaryDTO = performTopicSummary(requestId);

        // 4. 获取所有已完成的分片记录
        List<LlmRecord> completedSlices = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_EMOTION.getCode());

        DmsEmotionResponse responseDTO = new DmsEmotionResponse();
        List<TagAnalysisDTO> tagDataList = new ArrayList<>();


        for (LlmRecord llmRecord : completedSlices) {
            try {
                TagAnalysisDTO tagAnalysisDTO = JSONUtil.toBean(llmRecord.getLlmOutput(), TagAnalysisDTO.class);

                if (tagAnalysisDTO == null) {
                    log.warn("解析分片ID {} 的llm_output为空或无效，跳过该分片。", llmRecord.getId());
                    continue;
                }

                // 清理无效的问答对
                if (CollUtil.isNotEmpty(tagAnalysisDTO.getCustomerQuestionSummaries())) {
                    tagAnalysisDTO.getCustomerQuestionSummaries().removeIf(summary -> StrUtil.isBlank(summary.getQuestion()) || StrUtil.isBlank(summary.getAnswer()));
                }

                // 成功处理完一个分片，将其加入到最终的列表中
                tagDataList.add(tagAnalysisDTO);

            } catch (Exception e) {
                // 如果JSONUtil.toBean解析失败（比如llm_output格式损坏），会在这里捕获
                log.error("处理分片ID {} 时发生异常，已跳过该分片。异常信息: {}", llmRecord.getId(), e.getMessage());
            }
        }

        // 6. 如果经过筛选后，没有任何有效数据可以回调，则提前终止
        if (tagDataList.isEmpty()) {
            log.warn("请求ID: {} 的所有已完成分片均无法解析或数据无效，不执行回调。", requestId);
            return true;
        }

        // 7. 使用成功组装的数据列表进行后续操作
        responseDTO.setRequestId(requestId);
        responseDTO.setEmotionSummary(topicSummaryDTO != null ? topicSummaryDTO.getTotalEmotion() : null);
        responseDTO.setTagData(tagDataList);
        responseDTO.setTopicData(topicSummaryDTO);

        log.info("开始回调接口（共 {} 条有效数据）, 请求参数为: {}", tagDataList.size(), JSONUtil.toJsonStr(responseDTO));
        DgwResult dgwResult = dgwFeignClient.callBackDmsEmotion(responseDTO);
        log.info("回调接口成功, 接口返回: {}", JSONUtil.toJsonStr(dgwResult));

        return true;
    }



    private TopicSummaryDTO performTopicSummary(String requestId) {
        List<LlmRecord> allSlices = llmRecordMapper.selectByRequestId(requestId, null, LLMBizTypeEnum.DMS_EMOTION.getCode());
        if (CollUtil.isEmpty(allSlices)) {
            log.warn("无法进行话题总结，未找到请求ID {} 的任何分片记录", requestId);
            return null;
        }
        String fullContext = allSlices.stream()
                .map(LlmRecord::getUserInput)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining("\n"));
        if (StrUtil.isBlank(fullContext)) {
            log.warn("无法进行话题总结，请求ID {} 的所有分片内容为空", requestId);
            return null;
        }
        try {
            TopicSummaryDTO summaryDTO = BaiLianUtils.callForObject(
                    baiLianAppConfig.getEmotionWorkspaceId(),
                    baiLianAppConfig.getEmotionApiKey(),
                    baiLianAppConfig.getEmotionTopicAppId(),
                    fullContext,
                    TopicSummaryDTO.class
            );
            if (summaryDTO != null) {
                summaryDTO.setInput(fullContext);
            }
            log.info("话题总结结果: {}", JSONUtil.toJsonStr(summaryDTO));
            return summaryDTO;
        } catch (Exception e) {
            log.error("调用话题总结模型异常，requestId: {}", requestId, e);
            return null;
        }
    }

    public String downloadAndReadCosFile(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        byte[] fileContent;
        try (InputStream inputStream = url.openStream()) {
            fileContent = readAllBytes(inputStream);
        }
        String detectedEncoding = detectEncoding(fileContent);
        if (detectedEncoding == null) {
            detectedEncoding = "UTF-8";
            log.warn("无法检测文件编码, 将使用默认UTF-8. URL: {}", fileUrl);
        }
        return new String(fileContent, detectedEncoding);
    }

    private String detectEncoding(byte[] fileContent) {
        UniversalDetector detector = new UniversalDetector(null);
        detector.handleData(fileContent, 0, fileContent.length);
        detector.dataEnd();
        String encoding = detector.getDetectedCharset();
        detector.reset();
        return encoding;
    }

    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
            byte[] data = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, bytesRead);
            }
            return buffer.toByteArray();
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateStatusTran(Long id, String status, String remark) {
        LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LlmRecord::getId, id)
                .set(LlmRecord::getStatus, status)
                .set(LlmRecord::getRemark, remark);
        this.update(updateWrapper);
    }

    @Override
    public boolean retryFailedRecords(String requestId) {
        log.info("开始重试失败的记录，请求ID: {}", requestId);
        List<LlmRecord> failedRecords = llmRecordMapper.selectList(
                new LambdaQueryWrapper<LlmRecord>()
                        .eq(LlmRecord::getRequestId, requestId)
                        .eq(LlmRecord::getStatus, STATUS_FAILED)
        );
        if (CollUtil.isEmpty(failedRecords)) {
            log.info("没有找到失败的记录，请求ID: {}", requestId);
            return checkAndFinalizeProcessing(requestId);
        }
        for (LlmRecord record : failedRecords) {
            self.updateStatusTran(record.getId(), STATUS_UNPROCESSED, "重试任务");
        }
        log.info("已将 {} 条失败记录状态重置为未处理，准备重新执行。", failedRecords.size());
        return self.processSlice(requestId);
    }

    @Override
    public void cleanRequestData(String messageId) {
        log.info("开始清理消息ID: {} 的消息队列和分片数据", messageId);
        LambdaQueryWrapper<MessageQueue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageQueue::getMessageId, messageId)
                .eq(MessageQueue::getStatus, MessageStatus.PROCESSED.getCode());
        MessageQueue messageQueue = messageQueueMapper.selectOne(queryWrapper);
        if (messageQueue != null) {
            String requestId = messageQueue.getMessageContent();
            log.info("消息ID: {} 对应的请求ID为: {}", messageId, requestId);
            LambdaQueryWrapper<LlmRecord> llmQueryWrapper = new LambdaQueryWrapper<>();
            llmQueryWrapper.eq(LlmRecord::getRequestId, requestId);
            int deletedLlmRecords = llmRecordMapper.delete(llmQueryWrapper);
            log.info("成功清理请求ID: {} 对应的{}条llm_record记录", requestId, deletedLlmRecords);
            int deletedMessageQueues = messageQueueMapper.deleteById(messageQueue.getId());
            log.info("成功清理消息ID: {} 对应的{}条消息队列记录", messageId, deletedMessageQueues);
        } else {
            log.warn("消息ID: {} 没有找到对应的已处理消息队列记录", messageId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageQueueCleanResponse cleanCompletedMessages() {
        LambdaQueryWrapper<MessageQueue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageQueue::getStatus, MessageStatus.PROCESSED.getCode());
        List<MessageQueue> completedMessages = messageQueueMapper.selectList(queryWrapper);
        if (completedMessages.isEmpty()) {
            return MessageQueueCleanResponse.builder().cleanCount(0).requestIds(List.of()).build();
        }
        List<String> requestIds = completedMessages.stream()
                .map(MessageQueue::getMessageContent)
                .distinct()
                .collect(Collectors.toList());
        if (!requestIds.isEmpty()) {
            LambdaQueryWrapper<LlmRecord> llmQueryWrapper = new LambdaQueryWrapper<>();
            llmQueryWrapper.in(LlmRecord::getRequestId, requestIds);
            llmRecordMapper.delete(llmQueryWrapper);
            log.info("成功清理{}个请求ID对应的llm_record记录", requestIds.size());
        }
        List<Long> ids = completedMessages.stream().map(MessageQueue::getId).collect(Collectors.toList());
        int deletedCount = messageQueueMapper.deleteBatchIds(ids);
        log.info("成功清理{}条已完成的消息队列数据", deletedCount);
        return MessageQueueCleanResponse.builder().cleanCount(deletedCount).requestIds(requestIds).build();
    }

    /**
     * 【核心修改点】
     * 调用统一分析模型，接收扁平化结果，然后转换为结构化DTO并存入数据库。
     *
     * @param slice LlmRecord分片
     */
    private void performCombinedAnalysis(LlmRecord slice) {
        log.info("开始对记录ID: {} 进行统一分析（情绪+标签）", slice.getId());
        String userInput = slice.getUserInput();
        // 1. 调用LLM，使用临时DTO (LlmAnalysisResultDTO) 接收扁平化的JSON结果

        LlmAnalysisResultDTO llmResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getCombinedAnalysisAppId(),
                userInput,
                LlmAnalysisResultDTO.class
        );

        // 2. 将扁平化的llmResult映射到结构化的finalDto (TagAnalysisDTO)
        TagAnalysisDTO finalDto = new TagAnalysisDTO();
        finalDto.setLlmRecordId(slice.getId());
        finalDto.setStart(llmResult.getStart());
        finalDto.setEnd(llmResult.getEnd());
        finalDto.setCustomerQuestionSummaries(llmResult.getCustomerQuestionSummaries());
        finalDto.setDemandTags(llmResult.getDemandTags());
        finalDto.setAiDemandTags(llmResult.getAiDemandTags());

        // --- 这是关键的转换逻辑 ---
        // 创建嵌套的EmotionAnalysis对象
        TagAnalysisDTO.EmotionAnalysis semanticEmotion = new TagAnalysisDTO.EmotionAnalysis();
        semanticEmotion.setEmotion(llmResult.getCustomerEmotion());
        semanticEmotion.setReason(llmResult.getReason());
        semanticEmotion.setScore(llmResult.getScore());
        finalDto.setSemanticEmotion(semanticEmotion);

        // 语气情绪分析
        TagAnalysisDTO.EmotionAnalysis toneResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionToneAppId(),
                userInput,
                TagAnalysisDTO.EmotionAnalysis.class
        );
        TagAnalysisDTO.EmotionAnalysis toneEmotion = new TagAnalysisDTO.EmotionAnalysis();
        toneEmotion.setEmotion(toneResult.getEmotion());
        toneEmotion.setReason(toneResult.getReason());
        toneEmotion.setScore(toneResult.getScore());
        finalDto.setToneEmotion(toneEmotion);


        // 行为情绪分析
        TagAnalysisDTO.EmotionAnalysis actionEmotion = new TagAnalysisDTO.EmotionAnalysis();
        actionEmotion.setEmotion(llmResult.getCustomerEmotion());
        actionEmotion.setReason(llmResult.getReason());
        actionEmotion.setScore(llmResult.getScore());
        finalDto.setBehaviorEmotion(actionEmotion);


        LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LlmRecord::getId, slice.getId())
                .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(finalDto));

        this.update(updateWrapper);
        log.info("记录ID: {} 统一分析成功，并已将转换后格式的数据存入数据库。", slice.getId());

    }
}
