package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * FAQ机器人实体类
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("faq_robot")
public class FaqRobotPO {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 机器人名称
     */
    private String robotName;
    
    /**
     * 机器人描述
     */
    private String description;
    
    /**
     * 发布状态(0未发布 1 已发布 2 发布中 10 发布失败）
     */
    private Integer status;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新人
     */
    private String updatedBy;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 机器人发布时间
     */
    private String publishTime;

    /**
     * 机器人发布人
     */
    private String publishName;

    /**
     * 测试环境状态
     */
    @TableField(exist = false)
    private String textStatus;


    /**
     * 生产环境状态
     */
    @TableField(exist = false)
    private String prodStatus;

} 